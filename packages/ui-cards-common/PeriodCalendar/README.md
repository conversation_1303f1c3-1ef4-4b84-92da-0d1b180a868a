# PeriodCalendar 经期日历组件

一个功能完整的女性经期记录日历组件，参考美柚和大姨妈的设计理念，提供经期记录、周期预测、排卵期计算等功能。

## 功能特性

- 📅 **日历视图** - 清晰的月份日历展示
- 🩸 **经期记录** - 记录经期开始/结束日期
- 📊 **智能预测** - 基于历史数据预测下次经期和排卵期
- 💊 **症状记录** - 记录经期相关症状（痛经、头痛等）
- 📈 **流量记录** - 记录经期流量（轻量、中等、大量等）
- 📝 **备注功能** - 添加个人备注信息
- 🎨 **美观UI** - 参考主流经期应用的设计风格
- 📱 **响应式** - 适配不同屏幕尺寸

## 安装使用

```tsx
import {PeriodCalendar} from '@baidu/vita-ui-cards-common';
import type {DayRecord, PeriodCycle} from '@baidu/vita-ui-cards-common';
```

## 基础用法

```tsx
import React, {useState} from 'react';
import {PeriodCalendar} from '@baidu/vita-ui-cards-common';
import type {DayRecord} from '@baidu/vita-ui-cards-common';

const MyComponent = () => {
    const [records, setRecords] = useState<DayRecord[]>([]);
    
    const handleRecordUpdate = (record: DayRecord) => {
        setRecords(prev => {
            const index = prev.findIndex(r => r.date === record.date);
            if (index >= 0) {
                const newRecords = [...prev];
                newRecords[index] = record;
                return newRecords;
            } else {
                return [...prev, record];
            }
        });
    };
    
    const handleRecordDelete = (date: string) => {
        setRecords(prev => prev.filter(r => r.date !== date));
    };
    
    return (
        <PeriodCalendar
            records={records}
            onRecordUpdate={handleRecordUpdate}
            onRecordDelete={handleRecordDelete}
            showPrediction={true}
        />
    );
};
```

## 高级用法

```tsx
import React, {useState, useEffect} from 'react';
import {PeriodCalendar} from '@baidu/vita-ui-cards-common';
import type {DayRecord, PeriodCycle} from '@baidu/vita-ui-cards-common';

const AdvancedPeriodCalendar = () => {
    const [currentMonth, setCurrentMonth] = useState('2024-08');
    const [records, setRecords] = useState<DayRecord[]>([]);
    const [cycles, setCycles] = useState<PeriodCycle[]>([]);
    
    // 模拟加载历史数据
    useEffect(() => {
        // 这里可以从API加载数据
        const mockRecords: DayRecord[] = [
            {
                date: '2024-08-01',
                status: 'period',
                isPeriod: true,
                flow: 'medium',
                symptoms: ['cramps', 'fatigue'],
                note: '第一天，感觉还好'
            },
            {
                date: '2024-08-02',
                status: 'period',
                isPeriod: true,
                flow: 'heavy',
                symptoms: ['cramps', 'headache'],
            },
            // ... 更多记录
        ];
        
        setRecords(mockRecords);
    }, []);
    
    const handleDateClick = (date: string, record?: DayRecord) => {
        console.log('点击日期:', date, record);
    };
    
    const handleMonthChange = (month: string) => {
        setCurrentMonth(month);
        // 可以在这里加载新月份的数据
    };
    
    return (
        <PeriodCalendar
            currentMonth={currentMonth}
            records={records}
            cycles={cycles}
            showPrediction={true}
            onDateClick={handleDateClick}
            onRecordUpdate={(record) => {
                setRecords(prev => {
                    const index = prev.findIndex(r => r.date === record.date);
                    if (index >= 0) {
                        const newRecords = [...prev];
                        newRecords[index] = record;
                        return newRecords;
                    } else {
                        return [...prev, record];
                    }
                });
            }}
            onRecordDelete={(date) => {
                setRecords(prev => prev.filter(r => r.date !== date));
            }}
            onMonthChange={handleMonthChange}
        />
    );
};
```

## API 文档

### PeriodCalendar Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| currentMonth | string | 当前月份 | 显示的月份，格式：YYYY-MM |
| records | DayRecord[] | [] | 经期记录数据 |
| cycles | PeriodCycle[] | [] | 经期周期数据 |
| showPrediction | boolean | true | 是否显示预测数据 |
| readonly | boolean | false | 是否为只读模式 |
| className | string | - | 自定义样式类名 |
| onDateClick | (date: string, record?: DayRecord) => void | - | 日期点击回调 |
| onRecordUpdate | (record: DayRecord) => void | - | 记录更新回调 |
| onRecordDelete | (date: string) => void | - | 记录删除回调 |
| onMonthChange | (month: string) => void | - | 月份切换回调 |

### DayRecord 类型

```tsx
interface DayRecord {
    date: string;                    // 日期 YYYY-MM-DD
    status: PeriodStatus;           // 经期状态
    isPeriod: boolean;              // 是否为经期
    flow?: PeriodFlow;              // 经期流量
    symptoms: PeriodSymptom[];      // 症状列表
    note?: string;                  // 备注
    isPredicted?: boolean;          // 是否为预测数据
}
```

### PeriodStatus 枚举

- `NONE` - 无状态
- `PERIOD` - 经期中
- `OVULATION` - 排卵期
- `PREDICTED_PERIOD` - 预测经期
- `PREDICTED_OVULATION` - 预测排卵期
- `SAFE` - 安全期

### PeriodFlow 枚举

- `SPOTTING` - 极少
- `LIGHT` - 轻量
- `MEDIUM` - 中等
- `HEAVY` - 大量

### PeriodSymptom 枚举

- `CRAMPS` - 痛经
- `HEADACHE` - 头痛
- `BACK_PAIN` - 腰痛
- `BREAST_TENDERNESS` - 乳房胀痛
- `MOOD_SWINGS` - 情绪波动
- `FATIGUE` - 疲劳
- `BLOATING` - 腹胀
- `INSOMNIA` - 失眠

## 样式定制

组件使用 CSS Modules，可以通过覆盖样式类来自定义外观：

```less
// 自定义经期颜色
:global(.period-calendar) {
    .period {
        background: linear-gradient(135deg, #your-color 0%, #your-color-dark 100%);
    }
}
```

## 注意事项

1. 组件依赖 `dayjs` 进行日期处理
2. 使用了 Taro 的组件系统，确保在 Taro 环境中使用
3. 预测算法基于历史数据，建议至少有 3 个完整周期的数据才能获得较准确的预测
4. 组件支持响应式设计，在不同屏幕尺寸下都有良好的显示效果

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的经期记录和预测功能
- 提供完整的 UI 交互体验
