/**
 * 经期记录模态框组件
 */
import React, {memo, useState, useEffect, useCallback} from 'react';
import {View, Text, Button} from '@tarojs/components';
import cx from 'classnames';
import dayjs from 'dayjs';

import type {RecordModalProps, DayRecord, PeriodFlow, PeriodSymptom, PeriodStatus} from '../../types';
import {formatDate} from '../../utils/periodCalculator';
import Portal from '../../../Portal';

import styles from './index.module.less';

// 症状选项
const SYMPTOM_OPTIONS = [
    {key: 'cramps', label: '痛经', icon: '😣'},
    {key: 'headache', label: '头痛', icon: '🤕'},
    {key: 'back_pain', label: '腰痛', icon: '😰'},
    {key: 'breast_tenderness', label: '乳房胀痛', icon: '😔'},
    {key: 'mood_swings', label: '情绪波动', icon: '😢'},
    {key: 'fatigue', label: '疲劳', icon: '😴'},
    {key: 'bloating', label: '腹胀', icon: '🤰'},
    {key: 'insomnia', label: '失眠', icon: '😵'}
];

// 流量选项
const FLOW_OPTIONS = [
    {key: 'spotting', label: '极少', color: '#ffcccb'},
    {key: 'light', label: '轻量', color: '#ff9999'},
    {key: 'medium', label: '中等', color: '#ff6666'},
    {key: 'heavy', label: '大量', color: '#ff3333'}
];

const RecordModal: React.FC<RecordModalProps> = ({
    visible,
    selectedDate,
    record,
    onClose,
    onSave,
    onDelete
}) => {
    const [isPeriod, setIsPeriod] = useState(false);
    const [selectedFlow, setSelectedFlow] = useState<PeriodFlow | undefined>();
    const [selectedSymptoms, setSelectedSymptoms] = useState<PeriodSymptom[]>([]);
    const [note, setNote] = useState('');

    // 初始化表单数据
    useEffect(() => {
        if (record) {
            setIsPeriod(record.isPeriod);
            setSelectedFlow(record.flow);
            setSelectedSymptoms([...record.symptoms]);
            setNote(record.note || '');
        } else {
            setIsPeriod(false);
            setSelectedFlow(undefined);
            setSelectedSymptoms([]);
            setNote('');
        }
    }, [record, visible]);

    // 切换症状选择
    const toggleSymptom = useCallback((symptom: PeriodSymptom) => {
        setSelectedSymptoms(prev => {
            if (prev.includes(symptom)) {
                return prev.filter(s => s !== symptom);
            } else {
                return [...prev, symptom];
            }
        });
    }, []);

    // 保存记录
    const handleSave = useCallback(() => {
        const newRecord: DayRecord = {
            date: selectedDate,
            status: isPeriod ? PeriodStatus.PERIOD : PeriodStatus.NONE,
            isPeriod,
            flow: isPeriod ? selectedFlow : undefined,
            symptoms: selectedSymptoms,
            note: note.trim() || undefined,
            isPredicted: false
        };

        onSave(newRecord);
        onClose();
    }, [selectedDate, isPeriod, selectedFlow, selectedSymptoms, note, onSave, onClose]);

    // 删除记录
    const handleDelete = useCallback(() => {
        if (onDelete) {
            onDelete(selectedDate);
        }
        onClose();
    }, [selectedDate, onDelete, onClose]);

    if (!visible) return null;

    const formattedDate = dayjs(selectedDate).format('YYYY年MM月DD日');
    const weekday = dayjs(selectedDate).format('dddd');

    return (
        <Portal>
            <View className={styles.modalOverlay} onClick={onClose}>
                <View className={styles.modalContent} onClick={e => e.stopPropagation()}>
                    {/* 标题 */}
                    <View className={styles.modalHeader}>
                        <Text className={styles.modalTitle}>记录经期</Text>
                        <Text className={styles.modalDate}>{formattedDate} {weekday}</Text>
                        <View className={styles.closeButton} onClick={onClose}>
                            <Text className={styles.closeIcon}>×</Text>
                        </View>
                    </View>

                    {/* 内容 */}
                    <View className={styles.modalBody}>
                        {/* 经期开关 */}
                        <View className={styles.section}>
                            <View className={styles.sectionTitle}>
                                <Text className={styles.titleText}>是否为经期</Text>
                            </View>
                            <View className={styles.periodToggle}>
                                <View
                                    className={cx(styles.toggleOption, {
                                        [styles.active]: !isPeriod
                                    })}
                                    onClick={() => setIsPeriod(false)}
                                >
                                    <Text className={styles.toggleText}>否</Text>
                                </View>
                                <View
                                    className={cx(styles.toggleOption, {
                                        [styles.active]: isPeriod
                                    })}
                                    onClick={() => setIsPeriod(true)}
                                >
                                    <Text className={styles.toggleText}>是</Text>
                                </View>
                            </View>
                        </View>

                        {/* 流量选择 */}
                        {isPeriod && (
                            <View className={styles.section}>
                                <View className={styles.sectionTitle}>
                                    <Text className={styles.titleText}>流量</Text>
                                </View>
                                <View className={styles.flowOptions}>
                                    {FLOW_OPTIONS.map(option => (
                                        <View
                                            key={option.key}
                                            className={cx(styles.flowOption, {
                                                [styles.active]: selectedFlow === option.key
                                            })}
                                            style={{borderColor: option.color}}
                                            onClick={() => setSelectedFlow(option.key as PeriodFlow)}
                                        >
                                            <View
                                                className={styles.flowColor}
                                                style={{backgroundColor: option.color}}
                                            />
                                            <Text className={styles.flowLabel}>{option.label}</Text>
                                        </View>
                                    ))}
                                </View>
                            </View>
                        )}

                        {/* 症状选择 */}
                        <View className={styles.section}>
                            <View className={styles.sectionTitle}>
                                <Text className={styles.titleText}>症状</Text>
                                <Text className={styles.subtitle}>可多选</Text>
                            </View>
                            <View className={styles.symptomsGrid}>
                                {SYMPTOM_OPTIONS.map(option => (
                                    <View
                                        key={option.key}
                                        className={cx(styles.symptomOption, {
                                            [styles.active]: selectedSymptoms.includes(option.key as PeriodSymptom)
                                        })}
                                        onClick={() => toggleSymptom(option.key as PeriodSymptom)}
                                    >
                                        <Text className={styles.symptomIcon}>{option.icon}</Text>
                                        <Text className={styles.symptomLabel}>{option.label}</Text>
                                    </View>
                                ))}
                            </View>
                        </View>

                        {/* 备注 */}
                        <View className={styles.section}>
                            <View className={styles.sectionTitle}>
                                <Text className={styles.titleText}>备注</Text>
                            </View>
                            <View className={styles.noteInput}>
                                <input
                                    className={styles.noteTextarea}
                                    value={note}
                                    onChange={(e) => setNote(e.target.value)}
                                    placeholder="点击添加备注..."
                                    maxLength={200}
                                />
                            </View>
                        </View>
                    </View>

                    {/* 底部按钮 */}
                    <View className={styles.modalFooter}>
                        {record && onDelete && (
                            <Button
                                className={styles.deleteButton}
                                onClick={handleDelete}
                            >
                                删除记录
                            </Button>
                        )}
                        <View className={styles.actionButtons}>
                            <Button
                                className={styles.cancelButton}
                                onClick={onClose}
                            >
                                取消
                            </Button>
                            <Button
                                className={styles.saveButton}
                                onClick={handleSave}
                            >
                                保存
                            </Button>
                        </View>
                    </View>
                </View>
            </View>
        </Portal>
    );
};

RecordModal.displayName = 'RecordModal';

export default memo(RecordModal);
