/* 经期记录模态框样式 */
.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease;
}

.modalContent {
    background: #fff;
    border-radius: 32px 32px 0 0;
    width: 100%;
    max-width: 750px;
    max-height: 80vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* 模态框头部 */
.modalHeader {
    position: relative;
    padding: 32px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
}

.modalTitle {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.modalDate {
    font-size: 28px;
    color: #666;
}

.closeButton {
    position: absolute;
    top: 24px;
    right: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f5f5f5;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
        background: #e0e0e0;
    }
    
    &:active {
        transform: scale(0.95);
    }
}

.closeIcon {
    font-size: 32px;
    color: #666;
    line-height: 1;
}

/* 模态框内容 */
.modalBody {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.section {
    margin-bottom: 32px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.sectionTitle {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.titleText {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-right: 12px;
}

.subtitle {
    font-size: 24px;
    color: #999;
}

/* 经期开关 */
.periodToggle {
    display: flex;
    background: #f5f5f5;
    border-radius: 16px;
    padding: 4px;
}

.toggleOption {
    flex: 1;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.active {
        background: #ff6b6b;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        
        .toggleText {
            color: #fff;
            font-weight: 600;
        }
    }
}

.toggleText {
    font-size: 28px;
    color: #666;
    transition: all 0.2s ease;
}

/* 流量选择 */
.flowOptions {
    display: flex;
    gap: 16px;
}

.flowOption {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 12px;
    border: 2px solid transparent;
    border-radius: 16px;
    background: #f9f9f9;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.active {
        background: #fff;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    &:hover {
        transform: translateY(-1px);
    }
}

.flowColor {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-bottom: 8px;
}

.flowLabel {
    font-size: 24px;
    color: #333;
    font-weight: 500;
}

/* 症状网格 */
.symptomsGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.symptomOption {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid #f0f0f0;
    border-radius: 16px;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.active {
        border-color: #ff6b6b;
        background: #fff5f5;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.2);
    }
    
    &:hover {
        border-color: #ff6b6b;
        transform: translateY(-1px);
    }
}

.symptomIcon {
    font-size: 32px;
    margin-right: 12px;
}

.symptomLabel {
    font-size: 26px;
    color: #333;
    font-weight: 500;
}

/* 备注输入 */
.noteInput {
    width: 100%;
}

.noteTextarea {
    width: 100%;
    min-height: 120px;
    padding: 16px;
    border: 2px solid #f0f0f0;
    border-radius: 16px;
    font-size: 28px;
    color: #333;
    background: #fff;
    resize: vertical;
    outline: none;
    transition: all 0.2s ease;
    
    &:focus {
        border-color: #ff6b6b;
        box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.1);
    }
    
    &::placeholder {
        color: #999;
    }
}

/* 模态框底部 */
.modalFooter {
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.deleteButton {
    width: 100%;
    height: 88px;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 16px;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
        background: #ff3742;
        transform: translateY(-1px);
    }
    
    &:active {
        transform: translateY(0);
    }
}

.actionButtons {
    display: flex;
    gap: 16px;
}

.cancelButton,
.saveButton {
    flex: 1;
    height: 88px;
    border: none;
    border-radius: 16px;
    font-size: 28px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:active {
        transform: translateY(1px);
    }
}

.cancelButton {
    background: #f5f5f5;
    color: #666;
    
    &:hover {
        background: #e0e0e0;
    }
}

.saveButton {
    background: #ff6b6b;
    color: #fff;
    
    &:hover {
        background: #ff5252;
        transform: translateY(-1px);
    }
}

/* 响应式设计 */
@media (max-width: 750px) {
    .modalContent {
        border-radius: 24px 24px 0 0;
    }
    
    .modalHeader {
        padding: 24px 16px 12px;
    }
    
    .modalTitle {
        font-size: 32px;
    }
    
    .modalDate {
        font-size: 24px;
    }
    
    .modalBody {
        padding: 16px;
    }
    
    .titleText {
        font-size: 28px;
    }
    
    .toggleOption {
        height: 60px;
    }
    
    .toggleText {
        font-size: 24px;
    }
    
    .flowLabel,
    .symptomLabel {
        font-size: 22px;
    }
    
    .noteTextarea {
        font-size: 24px;
        min-height: 100px;
    }
    
    .modalFooter {
        padding: 16px;
    }
    
    .deleteButton,
    .cancelButton,
    .saveButton {
        height: 76px;
        font-size: 24px;
    }
}
