/* 日历网格样式 */
.calendarGrid {
    width: 100%;
    background: #fff;
    border-radius: 24px;
    padding: 32px 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 星期标题 */
.weekHeader {
    display: flex;
    margin-bottom: 16px;
}

.weekday {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
}

.weekdayText {
    font-size: 28px;
    font-weight: 500;
    color: #666;
}

/* 日期网格 */
.datesGrid {
    display: flex;
    flex-wrap: wrap;
}

.dateCell {
    width: 14.28571%; /* 100% / 7 */
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.dateContent {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.dateNumber {
    font-size: 28px;
    font-weight: 400;
    color: #333;
    line-height: 1;
}

/* 当前月份日期 */
.currentMonth .dateNumber {
    color: #333;
}

/* 其他月份日期 */
.otherMonth .dateNumber {
    color: #ccc;
}

/* 今天 */
.today {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff !important;
    
    .dateNumber {
        color: #fff;
        font-weight: 600;
    }
}

/* 可点击状态 */
.clickable {
    cursor: pointer;
    
    &:hover {
        background: #f5f5f5;
        transform: scale(1.05);
    }
    
    &:active {
        transform: scale(0.95);
    }
}

/* 经期状态样式 */
.period {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: #fff;
    
    .dateNumber {
        color: #fff;
        font-weight: 600;
    }
}

.periodPredicted {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.3) 0%, rgba(238, 90, 82, 0.3) 100%);
    border: 2px dashed #ff6b6b;
    
    .dateNumber {
        color: #ff6b6b;
        font-weight: 500;
    }
}

/* 排卵期状态 */
.ovulation {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: #fff;
    
    .dateNumber {
        color: #fff;
        font-weight: 600;
    }
}

.ovulationPredicted {
    background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, rgba(68, 160, 141, 0.3) 100%);
    border: 2px dashed #4ecdc4;
    
    .dateNumber {
        color: #4ecdc4;
        font-weight: 500;
    }
}

/* 安全期状态 */
.safe {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    
    .dateNumber {
        color: #2d5a3d;
        font-weight: 500;
    }
}

/* 症状指示器 */
.symptomsIndicator {
    position: absolute;
    top: 8px;
    right: 8px;
}

.symptomsDot {
    width: 12px;
    height: 12px;
    background: #ffd93d;
    border-radius: 50%;
    border: 2px solid #fff;
}

/* 流量指示器 */
.flowIndicator {
    position: absolute;
    bottom: 4px;
    right: 4px;
}

.flowText {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 2px 6px;
    line-height: 1;
}

/* 有记录的日期 */
.hasRecord {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 预测数据样式 */
.predicted {
    opacity: 0.7;
    
    &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 8px;
        height: 8px;
        background: #999;
        border-radius: 50%;
        opacity: 0.6;
    }
}

/* 实际数据样式 */
.actual {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 750px) {
    .calendarGrid {
        padding: 24px 16px;
        border-radius: 16px;
    }
    
    .dateCell {
        height: 70px;
        margin-bottom: 6px;
    }
    
    .dateNumber {
        font-size: 24px;
    }
    
    .weekdayText {
        font-size: 24px;
    }
    
    .flowText {
        font-size: 18px;
        padding: 1px 4px;
    }
    
    .symptomsDot {
        width: 10px;
        height: 10px;
    }
}
