/**
 * 日历网格组件
 */
import React, {memo, useMemo} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import dayjs from 'dayjs';

import type {CalendarGridProps, CalendarDay, PeriodStatus} from '../../types';
import {getCalendarGridDates, formatDate} from '../../utils/periodCalculator';

import styles from './index.module.less';

const WEEKDAYS = ['日', '一', '二', '三', '四', '五', '六'];

const CalendarGrid: React.FC<CalendarGridProps> = ({
    currentMonth,
    calendarDays,
    onDateClick,
    readonly = false
}) => {
    // 生成日历网格数据
    const gridDates = useMemo(() => {
        return getCalendarGridDates(currentMonth);
    }, [currentMonth]);

    // 将日期数据转换为日历日期对象
    const calendarData = useMemo(() => {
        const today = formatDate(new Date());
        const currentMonthStart = dayjs(currentMonth).startOf('month');
        const currentMonthEnd = dayjs(currentMonth).endOf('month');

        return gridDates.map(date => {
            const dateObj = dayjs(date);
            const isCurrentMonth = dateObj.isSameOrAfter(currentMonthStart) && 
                                 dateObj.isSameOrBefore(currentMonthEnd);
            
            const existingDay = calendarDays.find(day => day.date === date);
            
            return {
                date,
                day: dateObj.date(),
                isCurrentMonth,
                isToday: date === today,
                record: existingDay?.record
            } as CalendarDay;
        });
    }, [gridDates, calendarDays, currentMonth]);

    // 获取日期状态样式类名
    const getDateStatusClass = (day: CalendarDay): string => {
        if (!day.record) return '';
        
        const {status, isPredicted} = day.record;
        
        const baseClass = isPredicted ? 'predicted' : 'actual';
        
        switch (status) {
            case 'period':
                return `${styles.period} ${styles[baseClass]}`;
            case 'predicted_period':
                return `${styles.periodPredicted} ${styles[baseClass]}`;
            case 'ovulation':
                return `${styles.ovulation} ${styles[baseClass]}`;
            case 'predicted_ovulation':
                return `${styles.ovulationPredicted} ${styles[baseClass]}`;
            case 'safe':
                return `${styles.safe} ${styles[baseClass]}`;
            default:
                return '';
        }
    };

    // 获取日期显示内容
    const getDateContent = (day: CalendarDay): React.ReactNode => {
        const hasSymptoms = day.record?.symptoms && day.record.symptoms.length > 0;
        const hasFlow = day.record?.flow;
        
        return (
            <View className={styles.dateContent}>
                <Text className={styles.dateNumber}>{day.day}</Text>
                {hasSymptoms && (
                    <View className={styles.symptomsIndicator}>
                        <View className={styles.symptomsDot} />
                    </View>
                )}
                {hasFlow && (
                    <View className={styles.flowIndicator}>
                        <Text className={styles.flowText}>
                            {day.record?.flow === 'light' && '轻'}
                            {day.record?.flow === 'medium' && '中'}
                            {day.record?.flow === 'heavy' && '重'}
                            {day.record?.flow === 'spotting' && '少'}
                        </Text>
                    </View>
                )}
            </View>
        );
    };

    // 处理日期点击
    const handleDateClick = (day: CalendarDay) => {
        if (readonly || !day.isCurrentMonth) return;
        onDateClick(day.date, day.record);
    };

    return (
        <View className={styles.calendarGrid}>
            {/* 星期标题 */}
            <View className={styles.weekHeader}>
                {WEEKDAYS.map(weekday => (
                    <View key={weekday} className={styles.weekday}>
                        <Text className={styles.weekdayText}>{weekday}</Text>
                    </View>
                ))}
            </View>

            {/* 日期网格 */}
            <View className={styles.datesGrid}>
                {calendarData.map(day => (
                    <View
                        key={day.date}
                        className={cx(
                            styles.dateCell,
                            {
                                [styles.currentMonth]: day.isCurrentMonth,
                                [styles.otherMonth]: !day.isCurrentMonth,
                                [styles.today]: day.isToday,
                                [styles.clickable]: !readonly && day.isCurrentMonth,
                                [styles.hasRecord]: !!day.record
                            },
                            getDateStatusClass(day)
                        )}
                        onClick={() => handleDateClick(day)}
                    >
                        {getDateContent(day)}
                    </View>
                ))}
            </View>
        </View>
    );
};

CalendarGrid.displayName = 'CalendarGrid';

export default memo(CalendarGrid);
