/**
 * 经期计算工具函数
 */
import dayjs from 'dayjs';
import type {
    DayRecord,
    PeriodCycle,
    PeriodStats,
    PeriodCalculationConfig
} from '../types';
import { DEFAULT_PERIOD_CONFIG, PeriodStatus } from '../types';

/**
 * 计算两个日期之间的天数差
 */
export const getDaysDiff = (startDate: string, endDate: string): number => {
    return dayjs(endDate).diff(dayjs(startDate), 'day');
};

/**
 * 格式化日期为 YYYY-MM-DD
 */
export const formatDate = (date: Date | string | dayjs.Dayjs): string => {
    return dayjs(date).format('YYYY-MM-DD');
};

/**
 * 获取月份的所有日期
 */
export const getMonthDates = (month: string): string[] => {
    const startOfMonth = dayjs(month).startOf('month');
    const endOfMonth = dayjs(month).endOf('month');
    const dates: string[] = [];
    
    let current = startOfMonth;
    while (current.isBefore(endOfMonth) || current.isSame(endOfMonth)) {
        dates.push(formatDate(current));
        current = current.add(1, 'day');
    }
    
    return dates;
};

/**
 * 获取日历网格数据（包含上月末和下月初的日期）
 */
export const getCalendarGridDates = (month: string): string[] => {
    const startOfMonth = dayjs(month).startOf('month');
    const endOfMonth = dayjs(month).endOf('month');
    
    // 获取第一天是星期几（0=周日，1=周一...）
    const firstDayOfWeek = startOfMonth.day();
    
    const dates: string[] = [];
    
    // 添加上月末的日期
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        dates.push(formatDate(startOfMonth.subtract(i + 1, 'day')));
    }
    
    // 添加当月的日期
    let current = startOfMonth;
    while (current.isBefore(endOfMonth) || current.isSame(endOfMonth)) {
        dates.push(formatDate(current));
        current = current.add(1, 'day');
    }
    
    // 添加下月初的日期，补齐6行7列
    const remainingDays = 42 - dates.length; // 6行 * 7列 = 42
    for (let i = 1; i <= remainingDays; i++) {
        dates.push(formatDate(endOfMonth.add(i, 'day')));
    }
    
    return dates;
};

/**
 * 从记录中提取经期周期
 */
export const extractCyclesFromRecords = (records: DayRecord[]): PeriodCycle[] => {
    const periodRecords = records
        .filter(record => record.isPeriod && !record.isPredicted)
        .sort((a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf());
    
    if (periodRecords.length === 0) return [];
    
    const cycles: PeriodCycle[] = [];
    let currentCycle: Partial<PeriodCycle> | null = null;
    
    periodRecords.forEach((record, index) => {
        if (!currentCycle) {
            // 开始新周期
            currentCycle = {
                id: `cycle_${record.date}`,
                startDate: record.date
            };
        } else {
            const prevDate = periodRecords[index - 1]?.date;
            const daysDiff = getDaysDiff(prevDate, record.date);
            
            if (daysDiff > 1) {
                // 经期中断，结束当前周期，开始新周期
                currentCycle.endDate = prevDate;
                if (currentCycle.startDate) {
                    currentCycle.periodLength = getDaysDiff(currentCycle.startDate, currentCycle.endDate) + 1;
                    cycles.push(currentCycle as PeriodCycle);
                }
                
                currentCycle = {
                    id: `cycle_${record.date}`,
                    startDate: record.date
                };
            }
        }
        
        // 如果是最后一条记录，结束当前周期
        if (index === periodRecords.length - 1 && currentCycle) {
            currentCycle.endDate = record.date;
            if (currentCycle.startDate) {
                currentCycle.periodLength = getDaysDiff(currentCycle.startDate, currentCycle.endDate) + 1;
                cycles.push(currentCycle as PeriodCycle);
            }
        }
    });
    
    // 计算周期长度
    for (let i = 1; i < cycles.length; i++) {
        const prevCycle = cycles[i - 1];
        const currentCycle = cycles[i];
        prevCycle.cycleLength = getDaysDiff(prevCycle.startDate, currentCycle.startDate);
    }
    
    return cycles;
};

/**
 * 计算经期统计数据
 */
export const calculatePeriodStats = (
    cycles: PeriodCycle[],
    config: PeriodCalculationConfig = DEFAULT_PERIOD_CONFIG
): PeriodStats => {
    const validCycles = cycles.filter(cycle => cycle.cycleLength && cycle.periodLength);
    
    if (validCycles.length === 0) {
        return {
            averageCycleLength: config.defaultCycleLength,
            averagePeriodLength: config.defaultPeriodLength,
            shortestCycle: config.defaultCycleLength,
            longestCycle: config.defaultCycleLength,
            regularityScore: 0
        };
    }
    
    const cycleLengths = validCycles.map(cycle => cycle.cycleLength!);
    const periodLengths = validCycles.map(cycle => cycle.periodLength!);
    
    const averageCycleLength = Math.round(
        cycleLengths.reduce((sum, length) => sum + length, 0) / cycleLengths.length
    );
    
    const averagePeriodLength = Math.round(
        periodLengths.reduce((sum, length) => sum + length, 0) / periodLengths.length
    );
    
    const shortestCycle = Math.min(...cycleLengths);
    const longestCycle = Math.max(...cycleLengths);
    
    // 计算规律性评分（基于周期长度的标准差）
    const variance = cycleLengths.reduce((sum, length) => {
        return sum + Math.pow(length - averageCycleLength, 2);
    }, 0) / cycleLengths.length;
    
    const standardDeviation = Math.sqrt(variance);
    const regularityScore = Math.max(0, Math.min(100, 100 - standardDeviation * 10));
    
    // 预测下次经期
    const lastCycle = cycles[cycles.length - 1];
    let nextPeriodDate: string | undefined;
    let nextOvulationDate: string | undefined;
    
    if (lastCycle && lastCycle.endDate) {
        nextPeriodDate = formatDate(
            dayjs(lastCycle.startDate).add(averageCycleLength, 'day')
        );
        
        // 排卵期通常在下次经期前14天
        nextOvulationDate = formatDate(
            dayjs(nextPeriodDate).subtract(14, 'day')
        );
    }
    
    return {
        averageCycleLength,
        averagePeriodLength,
        shortestCycle,
        longestCycle,
        regularityScore: Math.round(regularityScore),
        nextPeriodDate,
        nextOvulationDate
    };
};

/**
 * 预测未来的经期和排卵期
 */
export const predictFuturePeriods = (
    stats: PeriodStats,
    _lastCycleEndDate: string,
    config: PeriodCalculationConfig = DEFAULT_PERIOD_CONFIG
): DayRecord[] => {
    const predictions: DayRecord[] = [];
    
    if (!stats.nextPeriodDate) return predictions;
    
    let currentPredictionDate = dayjs(stats.nextPeriodDate);
    
    for (let i = 0; i < config.predictionCycles; i++) {
        // 预测经期
        for (let j = 0; j < stats.averagePeriodLength; j++) {
            const date = formatDate(currentPredictionDate.add(j, 'day'));
            predictions.push({
                date,
                status: PeriodStatus.PREDICTED_PERIOD,
                isPeriod: true,
                symptoms: [],
                isPredicted: true
            });
        }
        
        // 预测排卵期
        const ovulationStart = currentPredictionDate.add(stats.averageCycleLength - 14, 'day');
        for (let j = 0; j < config.ovulationDays; j++) {
            const date = formatDate(ovulationStart.add(j, 'day'));
            predictions.push({
                date,
                status: PeriodStatus.PREDICTED_OVULATION,
                isPeriod: false,
                symptoms: [],
                isPredicted: true
            });
        }
        
        currentPredictionDate = currentPredictionDate.add(stats.averageCycleLength, 'day');
    }
    
    return predictions;
};

/**
 * 计算指定日期的经期状态
 */
export const calculateDayStatus = (
    date: string,
    records: DayRecord[],
    stats: PeriodStats
): PeriodStatus => {
    // 首先检查是否有实际记录
    const record = records.find(r => r.date === date && !r.isPredicted);
    if (record) {
        return record.status;
    }
    
    // 检查预测记录
    const predictedRecord = records.find(r => r.date === date && r.isPredicted);
    if (predictedRecord) {
        return predictedRecord.status;
    }
    
    // 如果没有记录，基于统计数据计算状态
    if (!stats.nextPeriodDate || !stats.nextOvulationDate) {
        return PeriodStatus.NONE;
    }
    
    const currentDate = dayjs(date);
    const nextOvulation = dayjs(stats.nextOvulationDate);
    
    // 检查是否在排卵期范围内
    if (currentDate.isAfter(nextOvulation.subtract(1, 'day')) && 
        currentDate.isBefore(nextOvulation.add(2, 'day'))) {
        return PeriodStatus.OVULATION;
    }
    
    // 其他情况为安全期
    return PeriodStatus.SAFE;
};

/**
 * 验证经期记录的合理性
 */
export const validatePeriodRecord = (record: DayRecord): string[] => {
    const errors: string[] = [];
    
    if (!record.date) {
        errors.push('日期不能为空');
    }
    
    if (record.isPeriod && record.symptoms.length === 0 && !record.flow) {
        errors.push('经期记录建议添加流量或症状信息');
    }
    
    return errors;
};
