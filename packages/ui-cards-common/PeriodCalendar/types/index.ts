/**
 * 经期日历组件类型定义
 */

// 经期状态枚举
export enum PeriodStatus {
    /** 无状态 */
    NONE = 'none',
    /** 经期中 */
    PERIOD = 'period',
    /** 排卵期 */
    OVULATION = 'ovulation',
    /** 预测经期 */
    PREDICTED_PERIOD = 'predicted_period',
    /** 预测排卵期 */
    PREDICTED_OVULATION = 'predicted_ovulation',
    /** 安全期 */
    SAFE = 'safe'
}

// 经期症状枚举
export enum PeriodSymptom {
    /** 痛经 */
    CRAMPS = 'cramps',
    /** 头痛 */
    HEADACHE = 'headache',
    /** 腰痛 */
    BACK_PAIN = 'back_pain',
    /** 乳房胀痛 */
    BREAST_TENDERNESS = 'breast_tenderness',
    /** 情绪波动 */
    MOOD_SWINGS = 'mood_swings',
    /** 疲劳 */
    FATIGUE = 'fatigue',
    /** 腹胀 */
    BLOATING = 'bloating',
    /** 失眠 */
    INSOMNIA = 'insomnia'
}

// 经期流量枚举
export enum PeriodFlow {
    /** 轻量 */
    LIGHT = 'light',
    /** 中等 */
    MEDIUM = 'medium',
    /** 大量 */
    HEAVY = 'heavy',
    /** 极少 */
    SPOTTING = 'spotting'
}

// 单日经期记录
export interface DayRecord {
    /** 日期 YYYY-MM-DD */
    date: string;
    /** 经期状态 */
    status: PeriodStatus;
    /** 是否为经期 */
    isPeriod: boolean;
    /** 经期流量 */
    flow?: PeriodFlow;
    /** 症状列表 */
    symptoms: PeriodSymptom[];
    /** 备注 */
    note?: string;
    /** 是否为预测数据 */
    isPredicted?: boolean;
}

// 经期周期数据
export interface PeriodCycle {
    /** 周期ID */
    id: string;
    /** 经期开始日期 */
    startDate: string;
    /** 经期结束日期 */
    endDate?: string;
    /** 周期长度（天） */
    cycleLength?: number;
    /** 经期天数 */
    periodLength?: number;
    /** 是否为预测周期 */
    isPredicted?: boolean;
}

// 经期统计数据
export interface PeriodStats {
    /** 平均周期长度 */
    averageCycleLength: number;
    /** 平均经期天数 */
    averagePeriodLength: number;
    /** 最短周期 */
    shortestCycle: number;
    /** 最长周期 */
    longestCycle: number;
    /** 周期规律性评分 (0-100) */
    regularityScore: number;
    /** 下次经期预测日期 */
    nextPeriodDate?: string;
    /** 下次排卵期预测日期 */
    nextOvulationDate?: string;
}

// 日历日期信息
export interface CalendarDay {
    /** 日期 YYYY-MM-DD */
    date: string;
    /** 日期数字 */
    day: number;
    /** 是否为当前月份 */
    isCurrentMonth: boolean;
    /** 是否为今天 */
    isToday: boolean;
    /** 经期记录 */
    record?: DayRecord;
}

// 组件Props
export interface PeriodCalendarProps {
    /** 当前显示月份 YYYY-MM */
    currentMonth?: string;
    /** 经期记录数据 */
    records?: DayRecord[];
    /** 经期周期数据 */
    cycles?: PeriodCycle[];
    /** 是否显示预测数据 */
    showPrediction?: boolean;
    /** 是否只读模式 */
    readonly?: boolean;
    /** 自定义样式类名 */
    className?: string;
    /** 日期点击回调 */
    onDateClick?: (date: string, record?: DayRecord) => void;
    /** 经期记录更新回调 */
    onRecordUpdate?: (record: DayRecord) => void;
    /** 经期记录删除回调 */
    onRecordDelete?: (date: string) => void;
    /** 月份切换回调 */
    onMonthChange?: (month: string) => void;
}

// 记录模态框Props
export interface RecordModalProps {
    /** 是否显示 */
    visible: boolean;
    /** 选中的日期 */
    selectedDate: string;
    /** 当前记录 */
    record?: DayRecord;
    /** 关闭回调 */
    onClose: () => void;
    /** 保存回调 */
    onSave: (record: DayRecord) => void;
    /** 删除回调 */
    onDelete?: (date: string) => void;
}

// 日历网格Props
export interface CalendarGridProps {
    /** 当前月份 YYYY-MM */
    currentMonth: string;
    /** 日历数据 */
    calendarDays: CalendarDay[];
    /** 日期点击回调 */
    onDateClick: (date: string, record?: DayRecord) => void;
    /** 是否只读 */
    readonly?: boolean;
}

// 月份导航Props
export interface MonthNavigatorProps {
    /** 当前月份 YYYY-MM */
    currentMonth: string;
    /** 月份切换回调 */
    onMonthChange: (month: string) => void;
}

// 经期计算配置
export interface PeriodCalculationConfig {
    /** 默认周期长度 */
    defaultCycleLength: number;
    /** 默认经期天数 */
    defaultPeriodLength: number;
    /** 排卵期天数 */
    ovulationDays: number;
    /** 预测周期数 */
    predictionCycles: number;
}

// 导出默认配置
export const DEFAULT_PERIOD_CONFIG: PeriodCalculationConfig = {
    defaultCycleLength: 28,
    defaultPeriodLength: 5,
    ovulationDays: 3,
    predictionCycles: 3
};
