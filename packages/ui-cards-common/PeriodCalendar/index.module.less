/* 经期日历主组件样式 */
.periodCalendar {
    width: 100%;
    background: linear-gradient(135deg, #ffeef8 0%, #fff5f5 100%);
    border-radius: 32px;
    padding: 32px 24px;
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.1);
    position: relative;
    overflow: hidden;
}

/* 装饰性背景 */
.periodCalendar::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 107, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

/* 头部区域 */
.header {
    margin-bottom: 32px;
    position: relative;
    z-index: 1;
}

/* 月份导航 */
.monthNavigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.navButton {
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
    
    &:active {
        transform: translateY(0);
    }
}

.navIcon {
    font-size: 48px;
    font-weight: 600;
    color: #ff6b6b;
    line-height: 1;
}

.monthDisplay {
    flex: 1;
    text-align: center;
    margin: 0 24px;
}

.monthText {
    font-size: 48px;
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 统计信息 */
.statsInfo {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px 24px;
    text-align: center;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.statsText {
    font-size: 26px;
    color: #666;
    margin-bottom: 8px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

/* 图例 */
.legend {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-top: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 107, 107, 0.1);
}

.legendItem {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legendColor {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.periodColor {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.ovulationColor {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.safeColor {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
}

.predictedColor {
    background: linear-gradient(135deg, #ddd 0%, #bbb 100%);
    border: 2px dashed #999;
}

.legendText {
    font-size: 24px;
    color: #666;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750px) {
    .periodCalendar {
        padding: 24px 16px;
        border-radius: 24px;
    }
    
    .header {
        margin-bottom: 24px;
    }
    
    .monthNavigation {
        margin-bottom: 12px;
    }
    
    .navButton {
        width: 60px;
        height: 60px;
    }
    
    .navIcon {
        font-size: 40px;
    }
    
    .monthDisplay {
        margin: 0 16px;
    }
    
    .monthText {
        font-size: 40px;
    }
    
    .statsInfo {
        padding: 16px 20px;
        border-radius: 16px;
    }
    
    .statsText {
        font-size: 22px;
        margin-bottom: 6px;
    }
    
    .legend {
        gap: 20px;
        margin-top: 24px;
        padding: 16px;
        border-radius: 16px;
        flex-wrap: wrap;
    }
    
    .legendColor {
        width: 20px;
        height: 20px;
    }
    
    .legendText {
        font-size: 20px;
    }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
    .periodCalendar {
        padding: 16px 12px;
        border-radius: 20px;
    }
    
    .navButton {
        width: 48px;
        height: 48px;
    }
    
    .navIcon {
        font-size: 32px;
    }
    
    .monthText {
        font-size: 32px;
    }
    
    .legend {
        gap: 16px;
        padding: 12px;
    }
    
    .legendItem {
        gap: 6px;
    }
    
    .legendColor {
        width: 16px;
        height: 16px;
    }
    
    .legendText {
        font-size: 18px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .periodCalendar {
        background: linear-gradient(135deg, #2a1f2a 0%, #3a2a3a 100%);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    
    .navButton {
        background: #444;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    
    .navIcon {
        color: #ff8a8a;
    }
    
    .monthText {
        color: #fff;
        background: linear-gradient(135deg, #ff8a8a 0%, #ff7070 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .statsInfo {
        background: rgba(68, 68, 68, 0.8);
        border: 1px solid rgba(255, 138, 138, 0.2);
    }
    
    .statsText {
        color: #ccc;
    }
    
    .legend {
        background: rgba(68, 68, 68, 0.6);
        border: 1px solid rgba(255, 138, 138, 0.1);
    }
    
    .legendText {
        color: #ccc;
    }
}

/* 动画效果 */
.periodCalendar {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.loading {
    .monthText,
    .statsText,
    .legendText {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
