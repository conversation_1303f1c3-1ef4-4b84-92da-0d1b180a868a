/**
 * 经期日历主组件
 */
import React, {memo, useState, useEffect, useMemo, useCallback} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import dayjs from 'dayjs';

import type {
    PeriodCalendarProps,
    DayRecord,
    CalendarDay,
    PeriodStats,
    PeriodCycle
} from './types';
import {
    formatDate,
    getCalendarGridDates,
    extractCyclesFromRecords,
    calculatePeriodStats,
    predictFuturePeriods,
    calculateDayStatus
} from './utils/periodCalculator';
import CalendarGrid from './components/CalendarGrid';
import RecordModal from './components/RecordModal';

import styles from './index.module.less';

const PeriodCalendar: React.FC<PeriodCalendarProps> = ({
    currentMonth: propCurrentMonth,
    records = [],
    cycles = [],
    showPrediction = true,
    readonly = false,
    className,
    onDateClick,
    onRecordUpdate,
    onRecordDelete,
    onMonthChange
}) => {
    // 当前显示月份
    const [currentMonth, setCurrentMonth] = useState(
        propCurrentMonth || formatDate(new Date()).substring(0, 7)
    );
    
    // 模态框状态
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedDate, setSelectedDate] = useState('');
    const [selectedRecord, setSelectedRecord] = useState<DayRecord | undefined>();

    // 同步外部传入的月份
    useEffect(() => {
        if (propCurrentMonth && propCurrentMonth !== currentMonth) {
            setCurrentMonth(propCurrentMonth);
        }
    }, [propCurrentMonth, currentMonth]);

    // 计算经期统计数据
    const periodStats = useMemo(() => {
        const extractedCycles = cycles.length > 0 ? cycles : extractCyclesFromRecords(records);
        return calculatePeriodStats(extractedCycles);
    }, [records, cycles]);

    // 生成预测数据
    const predictedRecords = useMemo(() => {
        if (!showPrediction || cycles.length === 0) return [];
        
        const lastCycle = cycles[cycles.length - 1];
        if (!lastCycle?.endDate) return [];
        
        return predictFuturePeriods(periodStats, lastCycle.endDate);
    }, [showPrediction, cycles, periodStats]);

    // 合并实际记录和预测记录
    const allRecords = useMemo(() => {
        return [...records, ...predictedRecords];
    }, [records, predictedRecords]);

    // 生成日历数据
    const calendarDays = useMemo(() => {
        const gridDates = getCalendarGridDates(currentMonth);
        const today = formatDate(new Date());
        const currentMonthStart = dayjs(currentMonth).startOf('month');
        const currentMonthEnd = dayjs(currentMonth).endOf('month');

        return gridDates.map(date => {
            const dateObj = dayjs(date);
            const isCurrentMonth = dateObj.isSameOrAfter(currentMonthStart) && 
                                 dateObj.isSameOrBefore(currentMonthEnd);
            
            const record = allRecords.find(r => r.date === date);
            
            return {
                date,
                day: dateObj.date(),
                isCurrentMonth,
                isToday: date === today,
                record
            } as CalendarDay;
        });
    }, [currentMonth, allRecords]);

    // 月份切换
    const handleMonthChange = useCallback((direction: 'prev' | 'next') => {
        const newMonth = dayjs(currentMonth)
            .add(direction === 'next' ? 1 : -1, 'month')
            .format('YYYY-MM');
        
        setCurrentMonth(newMonth);
        onMonthChange?.(newMonth);
    }, [currentMonth, onMonthChange]);

    // 日期点击处理
    const handleDateClick = useCallback((date: string, record?: DayRecord) => {
        if (readonly) return;
        
        setSelectedDate(date);
        setSelectedRecord(record);
        setModalVisible(true);
        
        onDateClick?.(date, record);
    }, [readonly, onDateClick]);

    // 记录保存处理
    const handleRecordSave = useCallback((record: DayRecord) => {
        onRecordUpdate?.(record);
        setModalVisible(false);
    }, [onRecordUpdate]);

    // 记录删除处理
    const handleRecordDelete = useCallback((date: string) => {
        onRecordDelete?.(date);
        setModalVisible(false);
    }, [onRecordDelete]);

    // 关闭模态框
    const handleModalClose = useCallback(() => {
        setModalVisible(false);
        setSelectedDate('');
        setSelectedRecord(undefined);
    }, []);

    // 格式化月份显示
    const monthDisplay = useMemo(() => {
        return dayjs(currentMonth).format('YYYY年MM月');
    }, [currentMonth]);

    return (
        <View className={cx(styles.periodCalendar, className)}>
            {/* 头部导航 */}
            <View className={styles.header}>
                <View className={styles.monthNavigation}>
                    <View
                        className={styles.navButton}
                        onClick={() => handleMonthChange('prev')}
                    >
                        <Text className={styles.navIcon}>‹</Text>
                    </View>
                    
                    <View className={styles.monthDisplay}>
                        <Text className={styles.monthText}>{monthDisplay}</Text>
                    </View>
                    
                    <View
                        className={styles.navButton}
                        onClick={() => handleMonthChange('next')}
                    >
                        <Text className={styles.navIcon}>›</Text>
                    </View>
                </View>

                {/* 统计信息 */}
                {periodStats.nextPeriodDate && (
                    <View className={styles.statsInfo}>
                        <Text className={styles.statsText}>
                            预计下次经期：{dayjs(periodStats.nextPeriodDate).format('MM月DD日')}
                        </Text>
                        {periodStats.nextOvulationDate && (
                            <Text className={styles.statsText}>
                                预计排卵期：{dayjs(periodStats.nextOvulationDate).format('MM月DD日')}
                            </Text>
                        )}
                    </View>
                )}
            </View>

            {/* 日历网格 */}
            <CalendarGrid
                currentMonth={currentMonth}
                calendarDays={calendarDays}
                onDateClick={handleDateClick}
                readonly={readonly}
            />

            {/* 图例 */}
            <View className={styles.legend}>
                <View className={styles.legendItem}>
                    <View className={cx(styles.legendColor, styles.periodColor)} />
                    <Text className={styles.legendText}>经期</Text>
                </View>
                <View className={styles.legendItem}>
                    <View className={cx(styles.legendColor, styles.ovulationColor)} />
                    <Text className={styles.legendText}>排卵期</Text>
                </View>
                <View className={styles.legendItem}>
                    <View className={cx(styles.legendColor, styles.safeColor)} />
                    <Text className={styles.legendText}>安全期</Text>
                </View>
                {showPrediction && (
                    <View className={styles.legendItem}>
                        <View className={cx(styles.legendColor, styles.predictedColor)} />
                        <Text className={styles.legendText}>预测</Text>
                    </View>
                )}
            </View>

            {/* 记录模态框 */}
            <RecordModal
                visible={modalVisible}
                selectedDate={selectedDate}
                record={selectedRecord}
                onClose={handleModalClose}
                onSave={handleRecordSave}
                onDelete={readonly ? undefined : handleRecordDelete}
            />
        </View>
    );
};

PeriodCalendar.displayName = 'PeriodCalendar';

export default memo(PeriodCalendar);
