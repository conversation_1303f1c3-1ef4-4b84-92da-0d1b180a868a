import {View} from '@tarojs/components';
import {useLoad} from '@tarojs/taro';
import {useState, useEffect} from 'react';
import cx from 'classnames';
import {HIcon, HPageContainer} from '@baidu/health-ui';
import {Button} from '@baidu/wz-taro-tools-core';
import {ImDemo} from '@baidu/vita-ui-cards-wz';
import {
    ImFlow,
    ImSystem,
    ImFormCard,
    CLoginButton,
    ImDoctorCard,
    Portal,
    PeriodCalendar,
    type DayRecord,
    type PeriodCycle
} from '@baidu/vita-ui-cards-common';

import ImCollectedAndNoNDirectSkuTypewriter from '../../components/ImCollectedAndNoNDirectSkuTypewriter';
import ImTopNavBar from '../../components/ImTopNavBar';
import StreamFocusService from '../../components/StreamFocusService';
import ImCollectedAndNoNDirectSku from '../../components/ImCollectedAndNoNDirectSku';
import HistoryRecordPopup from '../../components/HistoryRecordPopup';
import ImCollectedAndFocusSku from '../../components/ImCollectedAndReExpert';
import ImInput from '../../components/ImInput';
import CapsuleTools from '../../components/ImInput/capsuleTools';
import ImUploadGuide from '../../components/ImUploadGuide';

import {
    imFlowData,
    imSystemData,
    imFormCardData,
    ImCollectedAndNoNDirectSkuTypewriterData
} from './mockData';
import {Skudata} from './doctorCardMockData';
import {streamFocusServiceData} from './streamFocusServiceMockData';
import {ImCollectedAndNoNDirectSkuData} from './ImCollectedAndNoNDirectSkuMockData';
import {ImCollectedAndReExpertMockData} from './ImCollectedAndReExpertMockData';
import {ImUploadGuideData} from './ImUploadGuideMockData';
import ImImageDemo from './ImImageDemo';
import {navBarData} from './navBarMockData';
import {ongoingToast} from './guideMock';
import styles from './index.module.less';

export default function Index() {
    const [network, setNetwork] = useState<'init' | 'loading' | 'success' | 'error'>('loading');
    const [open, setOpen] = useState(false);

    // 经期日历状态
    const [periodRecords, setPeriodRecords] = useState<DayRecord[]>([]);
    const [periodCycles, setPeriodCycles] = useState<PeriodCycle[]>([]);

    useLoad(() => {
        // eslint-disable-next-line no-console
        console.log('Page loaded.');
    });

    // const init = (state: any) => {
    //     return {
    //         ...state,
    //         statusBarHeight: getSystemInfo().statusBarHeight, // 状态栏高度
    //         navigationBarHeight: getSystemInfo().navigationBarHeight // 导航栏高度
    //     };
    // };

    // const clickBtn = () => {
    //     navigateTo({url: '/pages/index/index'});
    // };

    useEffect(() => {
        setTimeout(() => {
            setNetwork('success');
        }, 1000);

        // 初始化经期日历测试数据
        const mockRecords: DayRecord[] = [
            {
                date: '2024-08-01',
                status: 'period' as const,
                isPeriod: true,
                flow: 'medium' as const,
                symptoms: ['cramps' as const, 'fatigue' as const],
                note: '第一天，感觉还好'
            },
            {
                date: '2024-08-02',
                status: 'period' as const,
                isPeriod: true,
                flow: 'heavy' as const,
                symptoms: ['cramps' as const, 'headache' as const],
                note: '流量较大'
            },
            {
                date: '2024-08-03',
                status: 'period' as const,
                isPeriod: true,
                flow: 'medium' as const,
                symptoms: ['back_pain' as const],
            },
            {
                date: '2024-08-04',
                status: 'period' as const,
                isPeriod: true,
                flow: 'light' as const,
                symptoms: [],
            },
            {
                date: '2024-08-05',
                status: 'period' as const,
                isPeriod: true,
                flow: 'spotting' as const,
                symptoms: [],
            },
            {
                date: '2024-08-15',
                status: 'ovulation' as const,
                isPeriod: false,
                symptoms: ['breast_tenderness' as const],
                note: '排卵期'
            }
        ];

        const mockCycles: PeriodCycle[] = [
            {
                id: 'cycle_2024_07',
                startDate: '2024-07-05',
                endDate: '2024-07-09',
                cycleLength: 28,
                periodLength: 5
            },
            {
                id: 'cycle_2024_08',
                startDate: '2024-08-01',
                endDate: '2024-08-05',
                cycleLength: 27,
                periodLength: 5
            }
        ];

        setPeriodRecords(mockRecords);
        setPeriodCycles(mockCycles);
    }, []);

    // 经期日历事件处理
    const handlePeriodRecordUpdate = (record: DayRecord) => {
        setPeriodRecords(prev => {
            const index = prev.findIndex(r => r.date === record.date);
            if (index >= 0) {
                const newRecords = [...prev];
                newRecords[index] = record;
                return newRecords;
            } else {
                return [...prev, record];
            }
        });
        // eslint-disable-next-line no-console
        console.log('更新经期记录:', record);
    };

    const handlePeriodRecordDelete = (date: string) => {
        setPeriodRecords(prev => prev.filter(r => r.date !== date));
        // eslint-disable-next-line no-console
        console.log('删除经期记录:', date);
    };

    const handleDateClick = (date: string, record?: DayRecord) => {
        // eslint-disable-next-line no-console
        console.log('点击日期:', date, record);
    };

    const handleMonthChange = (month: string) => {
        // eslint-disable-next-line no-console
        console.log('切换月份:', month);
    };

    return (
        <HPageContainer
            loadingConfig={{
                show: true
            }}
            pageNetworkStatus={network}
            background='#fff'
        >
            {/* <HTopBar
                title='百度健康问医生'
                barBg='linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%)'
                initialize={init}
                navigateBack={clickBtn}
                navigateHome={clickBtn}
            /> */}
            <Portal.provider>
                <View>
                    <ImDemo />
                    <ImTopNavBar
                        titleInfo={navBarData.topTips.orderGuideTip.titleInfo}
                        viewOrderInfo={navBarData.topTips.orderGuideTip.viewOrderInfo}
                        ongoingToast={ongoingToast}
                    />
                    <HIcon value='wise-assure' size={84} color='#333' />
                    <View className={cx(styles.ymz, 'c-row, c-gap-top-30')}>ymz</View>
                    <CLoginButton
                        isLogin={false}
                        closeShowNewUserTag={true}
                        useH5CodeLogin={true}
                        onLoginFail={error => {
                            // eslint-disable-next-line no-console
                            console.log('error', error);
                        }}
                        onLoginSuccess={async () => {
                            // eslint-disable-next-line no-console
                            console.log('登录成功');
                        }}
                    >
                        <Button color='primary'>我是登录按钮</Button>
                    </CLoginButton>
                    <ImUploadGuide msgId={ImUploadGuideData.msgId} data={ImUploadGuideData.data} />

                    <ImCollectedAndFocusSku
                        msgId={ImCollectedAndReExpertMockData.msgId}
                        data={ImCollectedAndReExpertMockData.data}
                    />

                    <ImFlow msgId={imFlowData.msgId} data={imFlowData.data} />
                    <StreamFocusService
                        msgId={streamFocusServiceData.msgId}
                        data={streamFocusServiceData.data}
                    />
                    <ImSystem data={imSystemData.data} />
                    <ImFlow msgId={imFlowData.msgId} data={imFlowData.data} />
                    <StreamFocusService
                        msgId={streamFocusServiceData.msgId}
                        data={streamFocusServiceData.data}
                    />
                    <ImSystem data={imSystemData.data} />

                    <ImDoctorCard data={Skudata.list[0]} />
                    <ImCollectedAndNoNDirectSku
                        msgId={ImCollectedAndNoNDirectSkuData.msgId}
                        data={ImCollectedAndNoNDirectSkuData.data}
                    />
                    <ImImageDemo />

                    <CapsuleTools />
                    <ImInput />
                    <Button
                        style={{paddingTop: '300'}}
                        color='primary'
                        onClick={() => {
                            setOpen(true);
                        }}
                    >
                        打开历史列表
                    </Button>
                    <HistoryRecordPopup {...{open, setOpen}}></HistoryRecordPopup>

                    <ImFormCard
                        msgId={ImCollectedAndNoNDirectSkuData.msgId}
                        data={imFormCardData.data}
                    />

                    <ImCollectedAndNoNDirectSkuTypewriter
                        cardId={ImCollectedAndNoNDirectSkuTypewriterData.data.content.cardId.toString()}
                        cardName={ImCollectedAndNoNDirectSkuTypewriterData.data.content.cardName}
                        version={1}
                        data={ImCollectedAndNoNDirectSkuTypewriterData.data.content.data}
                        msgId={ImCollectedAndNoNDirectSkuTypewriterData.meta.msgId}
                    ></ImCollectedAndNoNDirectSkuTypewriter>

                    {/* 经期日历组件测试 */}
                    <View style={{margin: '40px 0', padding: '20px', background: '#f5f5f5', borderRadius: '16px'}}>
                        <View style={{marginBottom: '20px', fontSize: '32px', fontWeight: 'bold', color: '#333'}}>
                            经期日历组件测试
                        </View>
                        <PeriodCalendar
                            currentMonth='2024-08'
                            records={periodRecords}
                            cycles={periodCycles}
                            showPrediction={true}
                            onDateClick={handleDateClick}
                            onRecordUpdate={handlePeriodRecordUpdate}
                            onRecordDelete={handlePeriodRecordDelete}
                            onMonthChange={handleMonthChange}
                        />
                    </View>
                </View>
                <Portal.slot />
            </Portal.provider>
        </HPageContainer>
    );
}
